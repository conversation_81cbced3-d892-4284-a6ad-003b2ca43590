package com.iflytek.aicc.flow;

import com.iflytek.aicc.common.config.ThreadPoolConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.concurrent.ThreadPoolExecutor;

/**
 * 描述：流程管理启动类
 * <AUTHOR>
 * @date 2023/8/12 16:54
 */
@ComponentScan(basePackages = {"com.iflytek.aicc", "com.iflytek.outbound"})
@MapperScan(basePackages = {"com.iflytek.aicc.common.infrastructure.repository.mapper"})
@SpringBootApplication
@EnableAsync
@EnableSwagger2
public class Application {

    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }

    @Bean("commonExecutor")
    public ThreadPoolTaskExecutor commonExecutor(ThreadPoolConfig threadPoolConfig) {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(threadPoolConfig.getCore());
        executor.setMaxPoolSize(threadPoolConfig.getMaxSize());
        executor.setQueueCapacity(threadPoolConfig.getQueueCapacity());
        executor.setKeepAliveSeconds(threadPoolConfig.getKeepAlive());
        executor.setThreadNamePrefix("pool-commonExecutor-Service-");
        // 线程池对拒绝任务的处理策略
        // CallerRunsPolicy：由调用线程（提交任务的线程）处理该任务
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        // 初始化
        executor.initialize();
        return executor;
    }

}
