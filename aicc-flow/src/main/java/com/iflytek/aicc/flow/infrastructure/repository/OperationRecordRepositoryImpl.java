package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.flow.domain.manage.bo.OperationRecordListBO;
import com.iflytek.aicc.flow.domain.manage.entity.OperationRecordEntity;
import com.iflytek.aicc.flow.domain.manage.repository.OperationRecordRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.OperationRecordConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.OperationRecordDo;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.OperationRecordMapper;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/10/30 13:33
 * @Description 操作记录仓库
 */
@Service
@Slf4j
public class OperationRecordRepositoryImpl implements OperationRecordRepository {
    private final OperationRecordMapper operationRecordMapper;

    public OperationRecordRepositoryImpl(OperationRecordMapper operationRecordMapper) {
        this.operationRecordMapper = operationRecordMapper;
    }

    @Override
    public PageList<OperationRecordEntity> getListByRelId(OperationRecordListBO operationRecordListBO, Page page) {
        LambdaQueryWrapper<OperationRecordDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(OperationRecordDo::getType, operationRecordListBO.getType());
        lambdaQueryWrapper.eq(OperationRecordDo::getRelId, operationRecordListBO.getRelId());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(operationRecordListBO.getOperator()), OperationRecordDo::getOperator,
                operationRecordListBO.getOperator());
        lambdaQueryWrapper.ge(operationRecordListBO.getOperateStartTime() != null, OperationRecordDo::getOperateTime,
                operationRecordListBO.getOperateStartTime());
        lambdaQueryWrapper.le(operationRecordListBO.getOperateEndTime() != null, OperationRecordDo::getOperateTime,
                operationRecordListBO.getOperateEndTime());
        lambdaQueryWrapper.orderByDesc(OperationRecordDo::getOperateTime);
        PageList<OperationRecordDo> pageList = PageUtil.doPage(() -> operationRecordMapper.selectList(lambdaQueryWrapper), page);
        return OperationRecordConvertor.toEntityPageList(pageList);
    }

    @Override
    public void insertOperationRecord(List<OperationRecordEntity> recordEntityList) {
        //查询最大版本号
        Map<String, OperationRecordDo> maxVersionMap = operationRecordMapper.getMaxVersion(recordEntityList);
        for (OperationRecordEntity recordEntity : recordEntityList) {
            String relId = recordEntity.getRelId();
            OperationRecordDo operationRecordDo = maxVersionMap.get(relId);
            Integer maxVersion = null;
            if (operationRecordDo != null) {
                maxVersion = operationRecordDo.getRecordRevision();
            }
            recordEntity.setRecordRevision(maxVersion == null ? 1 : maxVersion + 1);
        }
        List<OperationRecordDo> list = OperationRecordConvertor.toDoList(recordEntityList);
        operationRecordMapper.batchInsert(list);
    }


}
