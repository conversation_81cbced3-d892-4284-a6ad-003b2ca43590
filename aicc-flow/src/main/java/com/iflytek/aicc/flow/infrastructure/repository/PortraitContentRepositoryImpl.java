package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitContentEntity;
import com.iflytek.aicc.flow.domain.manage.repository.PortraitContentRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.PortraitContentConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitContentDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.PortraitContentMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: z<PERSON> heng
 * @date: 2024-09-18 11:34
 * @description: 患者画像内容
 */
@Component
public class PortraitContentRepositoryImpl implements PortraitContentRepository {
    @Autowired
    private PortraitContentMapper portraitContentMapper;

    @Override
    public List<PortraitContentEntity> contentList(PortraitContentEntity portraitContentEntity) {
        LambdaQueryWrapper<PortraitContentDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortraitContentDO::getConfigId, portraitContentEntity.getConfigId());
        queryWrapper.eq(PortraitContentDO::getDeleted, 0);
        List<PortraitContentDO> portraitContentDOList = portraitContentMapper.selectList(queryWrapper);
        return PortraitContentConvertor.toEntityList(portraitContentDOList);
    }

    @Override
    public void insert(PortraitContentEntity portraitContentEntity) {
        PortraitContentDO portraitContentDO = PortraitContentConvertor.toDO(portraitContentEntity);
        portraitContentMapper.insert(portraitContentDO);
    }

    @Override
    public void update(PortraitContentEntity portraitContentEntity) {
        PortraitContentDO portraitContentDO = PortraitContentConvertor.toDO(portraitContentEntity);
        portraitContentMapper.updateById(portraitContentDO);
    }

    @Override
    public void delete(String loginUserId, Long id) {
        PortraitContentDO portraitContentDO = new PortraitContentDO();
        portraitContentDO.setId(id);
        portraitContentDO.setDeleted(1);
        portraitContentDO.setDeletedTime(new Date());
        portraitContentDO.setUpdater(loginUserId);
        portraitContentDO.setUpdatedTime(new Date());
        portraitContentMapper.updateById(portraitContentDO);
    }

    @Override
    public void batchInsert(List<PortraitContentEntity> portraitContentEntityList) {
        List<PortraitContentDO> portraitContentDOList = getContentDOList(portraitContentEntityList);
        portraitContentMapper.batchInsert(portraitContentDOList);
    }

    @Override
    public void batchUpdate(List<PortraitContentEntity> portraitContentEntityList) {
        List<PortraitContentDO> portraitContentDOList = getContentDOList(portraitContentEntityList);
        Map<Integer, List<PortraitContentDO>> listMap = portraitContentDOList.stream().collect(Collectors.groupingBy(PortraitContentDO::getDeleted));
        List<PortraitContentDO> updateList = listMap.get(0);
        if (!CollectionUtils.isEmpty(updateList)) {
            portraitContentMapper.batchUpdate(portraitContentDOList);
        }
        List<PortraitContentDO> deleteList = listMap.get(1);
        if (!CollectionUtils.isEmpty(deleteList)) {
            List<Long> list = deleteList.stream().map(PortraitContentDO::getId).collect(Collectors.toList());
            portraitContentMapper.deleteBatchIds(list);
        }
    }

    private static List<PortraitContentDO> getContentDOList(List<PortraitContentEntity> portraitContentEntityList) {
        List<PortraitContentDO> portraitContentDOList = new ArrayList<>(portraitContentEntityList.size());
        for (PortraitContentEntity portraitContentEntity : portraitContentEntityList) {
            PortraitContentDO portraitContentDO = PortraitContentConvertor.toDO(portraitContentEntity);
            portraitContentDOList.add(portraitContentDO);
        }
        return portraitContentDOList;
    }
}
