package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.flow.domain.manage.entity.PortraitVariableEntity;
import com.iflytek.aicc.flow.domain.manage.repository.PortraitVariableRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.PortraitVariableConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.PortraitVariableDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.PortraitVariableMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: z<PERSON> heng
 * @date: 2025-05-15 15:43
 * @description:
 */
@Component
public class PortraitVariableRepositoryImpl implements PortraitVariableRepository {

    @Autowired
    private PortraitVariableMapper portraitVariableMapper;

    @Override
    public int batchInsert(List<PortraitVariableEntity> portraitVariableEntityList) {
        List<PortraitVariableDO> variableDOList = getVariableDOList(portraitVariableEntityList);
        return portraitVariableMapper.batchInsert(variableDOList);
    }

    @Override
    public int batchUpdate(List<PortraitVariableEntity> portraitVariableEntityList) {
        List<PortraitVariableDO> variableDOList = getVariableDOList(portraitVariableEntityList);
        Map<Integer, List<PortraitVariableDO>> listMap = variableDOList.stream().collect(Collectors.groupingBy(PortraitVariableDO::getDeleted));
        List<PortraitVariableDO> updateList = listMap.get(0);
        int row = 0;
        if (!CollectionUtils.isEmpty(updateList)) {
            row = portraitVariableMapper.batchUpdate(updateList);
        }
        List<PortraitVariableDO> deleteList = listMap.get(1);
        if (!CollectionUtils.isEmpty(deleteList)) {
            List<Long> list = deleteList.stream().map(PortraitVariableDO::getId).collect(Collectors.toList());
            portraitVariableMapper.deleteBatchIds(list);
        }
        return row;
    }

    @Override
    public List<PortraitVariableEntity> variableList(PortraitVariableEntity portraitVariableEntity) {
        LambdaQueryWrapper<PortraitVariableDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PortraitVariableDO::getConfigId, portraitVariableEntity.getConfigId());
        queryWrapper.eq(PortraitVariableDO::getDeleted, 0);
        List<PortraitVariableDO> portraitContentDOList = portraitVariableMapper.selectList(queryWrapper);
        return PortraitVariableConvertor.toEntityList(portraitContentDOList);
    }

    private List<PortraitVariableDO> getVariableDOList(List<PortraitVariableEntity> portraitVariableEntityList) {
        List<PortraitVariableDO> variableDOList = new ArrayList<>(portraitVariableEntityList.size());
        for (PortraitVariableEntity portraitContentEntity : portraitVariableEntityList) {
            PortraitVariableDO portraitVariableDO = PortraitVariableConvertor.toDO(portraitContentEntity);
            variableDOList.add(portraitVariableDO);
        }
        return variableDOList;
    }
}
