package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.iflytek.aicc.flow.domain.manage.bo.PublishRecordListBO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechPublishRecordEntity;
import com.iflytek.aicc.flow.domain.manage.repository.PublishRecordRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.PublishRecordConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.PublishRecordDo;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechPublishRecordMapper;
import com.iflytek.fpva.uapcenter.pojo.UapUser;
import com.iflytek.fpva.uapcenter.service.UserOrgQueryApi;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2023/11/5 9:18
 * @Description 发布记录仓库实现类
 */
@Service
public class PublishRecordRepositoryImpl implements PublishRecordRepository {
    private final SpeechPublishRecordMapper speechPublishRecordMapper;
    private final UserOrgQueryApi userOrgQueryApi;

    public PublishRecordRepositoryImpl(SpeechPublishRecordMapper speechPublishRecordMapper, UserOrgQueryApi userOrgQueryApi) {
        this.speechPublishRecordMapper = speechPublishRecordMapper;
        this.userOrgQueryApi = userOrgQueryApi;
    }

    @Override
    public PageList<SpeechPublishRecordEntity> query(PublishRecordListBO publishRecordListBO, Page page) {
        LambdaQueryWrapper<PublishRecordDo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.ge(publishRecordListBO.getStartTime() != null, PublishRecordDo::getCreatedTime, publishRecordListBO.getStartTime());
        lambdaQueryWrapper.le(publishRecordListBO.getEndTime() != null, PublishRecordDo::getCreatedTime, publishRecordListBO.getEndTime());
        lambdaQueryWrapper.eq(StringUtils.isNotEmpty(publishRecordListBO.getOperator()), PublishRecordDo::getCreatedBy,
                publishRecordListBO.getOperator());
        lambdaQueryWrapper.eq(publishRecordListBO.getSpeechId() != null, PublishRecordDo::getSpeechId, publishRecordListBO.getSpeechId());
        lambdaQueryWrapper.eq(publishRecordListBO.getKind() != null, PublishRecordDo::getKind, publishRecordListBO.getKind());
        lambdaQueryWrapper.orderByDesc(PublishRecordDo::getCreatedTime);
        PageList<PublishRecordDo> pageList = PageUtil.doPage(() -> speechPublishRecordMapper.selectList(lambdaQueryWrapper), page);
        List<PublishRecordDo> data = pageList.getData();
        Map<String, String> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(data)) {
            getUapUserMap(data, map);
        }
        return PublishRecordConvertor.toEntityPageList(pageList, map);
    }

    @Override
    public void insertPublishRecord(SpeechPublishRecordEntity speechPublishRecordEntity) {
        PublishRecordDo publishRecordDo = PublishRecordConvertor.toDO(speechPublishRecordEntity);
        speechPublishRecordMapper.insert(publishRecordDo);
    }

    @Override
    public SpeechPublishRecordEntity getById(Long id) {
        PublishRecordDo publishRecordDo = speechPublishRecordMapper.selectById(id);
        return PublishRecordConvertor.toEntity(publishRecordDo);
    }

    private void getUapUserMap(List<PublishRecordDo> data, Map<String, String> map) {
        Set<String> collectSet = new HashSet<>();
        for (PublishRecordDo datum : data) {
            collectSet.add(datum.getCreatedBy());
        }
        List<UapUser> uapUserList = userOrgQueryApi.getUsersByUserIds(Lists.newArrayList(collectSet), null);
        for (UapUser uapUser : uapUserList) {
            map.put(uapUser.getId(), uapUser.getName());
        }
    }
}
