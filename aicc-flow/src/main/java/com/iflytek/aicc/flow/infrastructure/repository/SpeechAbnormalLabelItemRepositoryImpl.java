package com.iflytek.aicc.flow.infrastructure.repository;

import com.iflytek.aicc.flow.domain.manage.entity.SpeechAbnormalLabelItemEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechAbnormalLabelItemRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechAbnormalLabelItemConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechAbnormalLabelItemDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechAbnormalLabelItemMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: zhao heng
 * @date: 2025-01-09 16:40
 * @description:
 */
@Service
public class SpeechAbnormalLabelItemRepositoryImpl implements SpeechAbnormalLabelItemRepository {
    @Autowired
    private SpeechAbnormalLabelItemMapper speechAbnormalLabelItemMapper;

    @Override
    public int deleteByPrimaryKey(Long id) {
        return speechAbnormalLabelItemMapper.deleteByPrimaryKey(id);
    }

    @Override
    public int insert(SpeechAbnormalLabelItemDO record) {
        return 0;
    }

    @Override
    public int insertSelective(SpeechAbnormalLabelItemDO record) {
        return 0;
    }

    @Override
    public SpeechAbnormalLabelItemEntity selectByPrimaryKey(Long id) {
        return null;
    }

    @Override
    public int updateByPrimaryKeySelective(SpeechAbnormalLabelItemDO record) {
        return 0;
    }

    @Override
    public int updateByPrimaryKey(SpeechAbnormalLabelItemDO record) {
        return 0;
    }

    @Override
    public int batchInsert(List<SpeechAbnormalLabelItemEntity> list) {
        List<SpeechAbnormalLabelItemDO> labelItemDOList = SpeechAbnormalLabelItemConvertor.toDOList(list);
        return speechAbnormalLabelItemMapper.batchInsert(labelItemDOList);
    }

    @Override
    public void deleteByLabelIds(List<Long> labelIds) {
        speechAbnormalLabelItemMapper.deleteByLabelIds(labelIds);
    }

    @Override
    public List<SpeechAbnormalLabelItemEntity> selectByLabelId(Long labelId) {
        List<SpeechAbnormalLabelItemDO> abnormalLabelItemDOList = speechAbnormalLabelItemMapper.selectByLabelId(labelId);
        return SpeechAbnormalLabelItemConvertor.toEntityList(abnormalLabelItemDOList);
    }

    @Override
    public List<SpeechAbnormalLabelItemEntity> selectByLabelIds(List<Long> labelIds) {
        return null;
    }
}
