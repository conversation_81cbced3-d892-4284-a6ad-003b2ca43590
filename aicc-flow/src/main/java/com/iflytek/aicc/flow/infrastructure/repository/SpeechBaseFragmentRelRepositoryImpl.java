package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.flow.common.enums.SpeechFragmentRelEnums;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechFragmentRelUpdateBO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseFragmentRelEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechBaseFragmentRelRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechBaseFragmentRelConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechBaseFragmentRelDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechBaseFragmentRelMapper;
import com.iflytek.medicalboot.core.id.UidService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author: zhao heng
 * @date: 2024-09-02 8:30
 * @description:
 */
@Component
public class SpeechBaseFragmentRelRepositoryImpl implements SpeechBaseFragmentRelRepository {

    @Autowired
    private UidService uidService;
    @Autowired
    private SpeechBaseFragmentRelMapper speechBaseFragmentRelMapper;

    @Override
    public Integer updateRelStatusByFragmentId(Long id, String loginUserId) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechFragmentId, id);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getRelState, SpeechFragmentRelEnums.NORMAL.getCode());
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setUpdatedBy(loginUserId);
        speechBaseFragmentRelDO.setUpdatedTime(new Date());
        speechBaseFragmentRelDO.setRelState(SpeechFragmentRelEnums.PENDING.getCode());
        return speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);
    }

    @Override
    public Integer deleteAndInsert(List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntity) {
        if (CollectionUtils.isEmpty(speechBaseFragmentRelEntity)) {
            return 0;
        }
        //查询话术下的片段关系
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechId, speechBaseFragmentRelEntity.get(0).getSpeechId());
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        List<SpeechBaseFragmentRelDO> existSpeechBaseFragmentRelList = speechBaseFragmentRelMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(existSpeechBaseFragmentRelList)) {
            for (SpeechBaseFragmentRelEntity baseFragmentRelEntity : speechBaseFragmentRelEntity) {
                baseFragmentRelEntity.setId(uidService.getUID());
            }
            List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDOList = SpeechBaseFragmentRelConvertor.toDOList(speechBaseFragmentRelEntity);
            return speechBaseFragmentRelMapper.batchInsert(speechBaseFragmentRelDOList);
        }
        //删除话术下的片段
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setDeleted(1);
        speechBaseFragmentRelDO.setUpdatedTime(speechBaseFragmentRelEntity.get(0).getUpdatedTime());
        speechBaseFragmentRelDO.setUpdatedBy(speechBaseFragmentRelEntity.get(0).getUpdatedBy());
        speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);
        //重新保存
        //原始 话术片段-》状态
        Map<Long, Integer> fragmentIdMap = existSpeechBaseFragmentRelList.stream().collect(
                Collectors.toMap(SpeechBaseFragmentRelDO::getSpeechFragmentId, SpeechBaseFragmentRelDO::getRelState));
        for (SpeechBaseFragmentRelEntity baseFragmentRelEntity : speechBaseFragmentRelEntity) {
            baseFragmentRelEntity.setId(uidService.getUID());
            Integer relState = fragmentIdMap.get(baseFragmentRelEntity.getSpeechFragmentId());
            if (Objects.isNull(relState)) {
                relState = SpeechFragmentRelEnums.NORMAL.getCode();
            }
            baseFragmentRelEntity.setRelState(relState);
        }
        List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDOList = SpeechBaseFragmentRelConvertor.toDOList(speechBaseFragmentRelEntity);
        return speechBaseFragmentRelMapper.batchInsert(speechBaseFragmentRelDOList);

    }

    @Override
    public List<SpeechBaseFragmentRelEntity> listBySpeechId(Long id) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechId, id);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDO = speechBaseFragmentRelMapper.selectList(queryWrapper);
        return SpeechBaseFragmentRelConvertor.toEntityList(speechBaseFragmentRelDO);
    }

    @Override
    public Integer updateFragmentRelStatus(SpeechFragmentRelUpdateBO updateBO) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpeechBaseFragmentRelDO::getSpeechFragmentId, updateBO.getFragmentId());
        Long speechId = updateBO.getSpeechId();
        if (Objects.nonNull(speechId)) {
            queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechId, speechId);
        }
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getRelState, SpeechFragmentRelEnums.PENDING.getCode());
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setRelState(SpeechFragmentRelEnums.NORMAL.getCode());
        speechBaseFragmentRelDO.setUpdatedBy(updateBO.getLoginUserId());
        speechBaseFragmentRelDO.setUpdatedTime(new Date());
        return speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);
    }

    @Override
    public List<SpeechBaseFragmentRelEntity> listByFragmentId(Long id) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechFragmentId, id);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDO = speechBaseFragmentRelMapper.selectList(queryWrapper);
        return SpeechBaseFragmentRelConvertor.toEntityList(speechBaseFragmentRelDO);
    }

    @Override
    public void deleteByFragmentId(Long id, String loginUserId) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechFragmentId, id);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setDeleted(1);
        speechBaseFragmentRelDO.setUpdatedTime(new Date());
        speechBaseFragmentRelDO.setUpdatedBy(loginUserId);
        speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);

    }

    @Override
    public Integer deleteAndInsertByFragmentId(List<SpeechBaseFragmentRelEntity> speechBaseFragmentRelEntityList) {
        if (CollectionUtils.isEmpty(speechBaseFragmentRelEntityList)) {
            return 0;
        }
        //查询片段和话术对应关系
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechFragmentId, speechBaseFragmentRelEntityList.get(0).getSpeechFragmentId());
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        List<SpeechBaseFragmentRelDO> existSpeechBaseFragmentRelDOList = speechBaseFragmentRelMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(existSpeechBaseFragmentRelDOList)) {
            for (SpeechBaseFragmentRelEntity baseFragmentRelEntity : speechBaseFragmentRelEntityList) {
                baseFragmentRelEntity.setId(uidService.getUID());
            }
            List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDOList = SpeechBaseFragmentRelConvertor.toDOList(speechBaseFragmentRelEntityList);
            return speechBaseFragmentRelMapper.batchInsert(speechBaseFragmentRelDOList);
        }
        //删除
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setDeleted(1);
        speechBaseFragmentRelDO.setUpdatedTime(speechBaseFragmentRelEntityList.get(0).getUpdatedTime());
        speechBaseFragmentRelDO.setUpdatedBy(speechBaseFragmentRelEntityList.get(0).getUpdatedBy());
        speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);
        //  key:话术id，value：状态
        Map<Long, Integer> speechIdMap = existSpeechBaseFragmentRelDOList.stream().collect(
                Collectors.toMap(SpeechBaseFragmentRelDO::getSpeechId, SpeechBaseFragmentRelDO::getRelState));

        for (SpeechBaseFragmentRelEntity baseFragmentRelEntity : speechBaseFragmentRelEntityList) {
            baseFragmentRelEntity.setId(uidService.getUID());
            Long speechId = baseFragmentRelEntity.getSpeechId();
            Integer relState = speechIdMap.get(speechId);
            if (Objects.isNull(relState)) {
                relState = SpeechFragmentRelEnums.NORMAL.getCode();
            }
            baseFragmentRelEntity.setRelState(relState);

        }
        List<SpeechBaseFragmentRelDO> speechBaseFragmentRelDOList = SpeechBaseFragmentRelConvertor.toDOList(speechBaseFragmentRelEntityList);
        return speechBaseFragmentRelMapper.batchInsert(speechBaseFragmentRelDOList);
    }

    @Override
    public void deleteBySpeechId(Long speechId, String loginUserId) {
        LambdaQueryWrapper<SpeechBaseFragmentRelDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseFragmentRelDO::getSpeechId, speechId);
        queryWrapper.eq(SpeechBaseFragmentRelDO::getDeleted, 0);
        SpeechBaseFragmentRelDO speechBaseFragmentRelDO = new SpeechBaseFragmentRelDO();
        speechBaseFragmentRelDO.setDeleted(1);
        speechBaseFragmentRelDO.setUpdatedTime(new Date());
        speechBaseFragmentRelDO.setUpdatedBy(loginUserId);
        speechBaseFragmentRelMapper.update(speechBaseFragmentRelDO, queryWrapper);
    }
}
