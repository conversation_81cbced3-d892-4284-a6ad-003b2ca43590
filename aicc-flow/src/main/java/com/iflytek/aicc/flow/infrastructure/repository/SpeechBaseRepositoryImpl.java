package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iflytek.aicc.flow.common.enums.SpeechManagerEnums;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechFragmentListBO;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechManagerExportBO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechBaseEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechBaseRepository;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechFragmentRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechBaseConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechManagerConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechBaseDO;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechManagerListDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechBaseMapper;
import com.iflytek.medicalboot.core.id.UidService;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * 话术管理
 *
 * <AUTHOR>
 * @date 2023-08-17
 **/
@Service
public class SpeechBaseRepositoryImpl implements SpeechBaseRepository {

    private final SpeechBaseMapper speechBaseMapper;
    private final UidService uidService;
    private final SpeechFragmentRepository speechFragmentRepository;

    @Autowired
    public SpeechBaseRepositoryImpl(SpeechBaseMapper speechBaseMapper, UidService uidService,
                                    SpeechFragmentRepository speechFragmentRepository) {
        this.speechBaseMapper = speechBaseMapper;
        this.uidService = uidService;
        this.speechFragmentRepository = speechFragmentRepository;
    }

    @Override
    public Long insertSpeech(SpeechBaseEntity speechBaseEntity) {
        SpeechBaseDO speechBaseDO = SpeechManagerConvertor.toDo(speechBaseEntity);
        if (null == speechBaseEntity.getId()) {
            speechBaseDO.setId(uidService.getUID());
        }
        speechBaseDO.setCreatedBy(speechBaseEntity.getCreatedBy());
        speechBaseDO.setCreatedTime(new Date());
        speechBaseMapper.insert(speechBaseDO);
        return speechBaseDO.getId();
    }

    @Override
    public void updateSpeech(SpeechBaseEntity speechBaseEntity) {
        SpeechBaseDO speechManagerDO = SpeechManagerConvertor.toDo(speechBaseEntity);
        speechBaseMapper.updateSpeechById(speechManagerDO);
    }

    @Override
    public void deleteSpeech(String loginUserId, Long id) {
        speechBaseMapper.deleteSpeechById(loginUserId, id, new Date());
    }

    @Override
    public SpeechBaseEntity getSpeechBySpeechName(String speechName, Long id, Integer speechType) {
        return speechBaseMapper.getSpeechBySpeechName(speechName, id, speechType);
    }

    @Override
    public SpeechBaseEntity getManagerById(Long id) {
        return speechBaseMapper.getManagerById(id);
    }

    @Override
    public PageList<SpeechBaseEntity> getManagerList(SpeechManagerListDO listDO, Page page) {
        PageList<SpeechBaseDO> speechBaseDOS = new PageList<>();
        Integer speechType = listDO.getSpeechType();
        if (SpeechManagerEnums.SpeechTypeEnum.STATUS_NORMAL.getCode().equals(speechType)) {
            if (page != null) {
                speechBaseDOS = PageUtil.doPage(() -> speechBaseMapper.queryList(listDO), page);
            } else {
                List<SpeechBaseDO> speechBaseDOS1 = speechBaseMapper.queryList(listDO);
                speechBaseDOS.setData(speechBaseDOS1);
            }

        } else {
            List<String> speechFragmentNames = listDO.getSpeechFragmentNames();
            if (CollectionUtils.isNotEmpty(speechFragmentNames)) {
                List<Long> fragmentId = getFragmentId(speechFragmentNames);
                if (CollectionUtils.isEmpty(fragmentId)) {
                    return new PageList<>();
                }
                listDO.setSpeechFragmentIds(fragmentId);
            }
            if (page != null) {
                speechBaseDOS = PageUtil.doPage(() -> speechBaseMapper.listSpeech(listDO), page);
            } else {
                List<SpeechBaseDO> speechBaseDOS1 = speechBaseMapper.listSpeech(listDO);
                speechBaseDOS.setData(speechBaseDOS1);
            }
        }
        return SpeechBaseConvertor.toPageList(speechBaseDOS);
    }

    private List<Long> getFragmentId(List<String> speechFragmentNames) {
        SpeechFragmentListBO speechFragmentListBO = new SpeechFragmentListBO();
        speechFragmentListBO.setName(speechFragmentNames);
        return speechFragmentRepository.listByFragmentName(speechFragmentListBO);
    }


    private static LambdaQueryWrapper<SpeechBaseDO> getQueryWrapper(SpeechManagerListDO listDO, Integer speechType) {
        LambdaQueryWrapper<SpeechBaseDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechBaseDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        if (CollectionUtils.isNotEmpty(listDO.getIds())) {
            lambdaQueryWrapper.in(SpeechBaseDO::getId, listDO.getIds());
        }
        if (StringUtils.isNotBlank(listDO.getSpeechName())) {
            if (listDO.getFullMatchFlag() != null && listDO.getFullMatchFlag()) {
                lambdaQueryWrapper.eq(SpeechBaseDO::getSpeechName, listDO.getSpeechName());
            } else {
                lambdaQueryWrapper.like(SpeechBaseDO::getSpeechName, listDO.getSpeechName());
            }
        }
        if (StringUtils.isNotBlank(listDO.getCreator())) {
            lambdaQueryWrapper.eq(SpeechBaseDO::getCreatedBy, listDO.getCreator());
        }
        lambdaQueryWrapper.eq(SpeechBaseDO::getSpeechType, speechType);
        Integer status = listDO.getStatus();
        if (Objects.nonNull(status)) {
            lambdaQueryWrapper.eq(SpeechBaseDO::getStatus, status);
        }
        lambdaQueryWrapper.eq(listDO.getSpeechMode() != null, SpeechBaseDO::getSpeechMode, listDO.getSpeechMode());
        lambdaQueryWrapper.eq(listDO.getModelStage() != null, SpeechBaseDO::getModelStage, listDO.getModelStage());
        lambdaQueryWrapper.orderByDesc(SpeechBaseDO::getCreatedTime);
        Boolean deliveryRole = listDO.getDeliveryRole();
        Integer viewType = listDO.getViewType();
        //交付角色
        if (deliveryRole) {
            //标准话术资源（所有运营角色创建的话术）
            if (viewType == 1) {
                lambdaQueryWrapper.notIn(SpeechBaseDO::getCreatedBy, listDO.getUserIds());
            }
            //我的机构资源
            if (viewType == 4) {
                lambdaQueryWrapper.in(SpeechBaseDO::getCreatedBy, listDO.getUserIds());
                lambdaQueryWrapper.in(SpeechBaseDO::getPlatCode, listDO.getSelfPlatCodes());
            }
            //其他机构资源
            if (viewType == 3) {
                lambdaQueryWrapper.in(SpeechBaseDO::getCreatedBy, listDO.getUserIds());
                lambdaQueryWrapper.notIn(SpeechBaseDO::getPlatCode, listDO.getSelfPlatCodes());
                List<String> platCodes = listDO.getPlatCodes();
                if (CollectionUtils.isNotEmpty(platCodes)) {
                    lambdaQueryWrapper.in(SpeechBaseDO::getPlatCode, listDO.getPlatCodes());
                }
            }
        } else {
            //运管角色
            if (viewType == 1) {
                lambdaQueryWrapper.notIn(SpeechBaseDO::getCreatedBy, listDO.getUserIds());
            }
            if (viewType == 2) {
                lambdaQueryWrapper.in(SpeechBaseDO::getCreatedBy, listDO.getUserIds());
            }
        }
        return lambdaQueryWrapper;
    }

    @Override
    public SpeechBaseEntity getSpeechBaseById(Long id, Integer publishStatus) {
        LambdaQueryWrapper<SpeechBaseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseDO::getId, id);
        queryWrapper.eq(SpeechBaseDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        if (null != publishStatus) {
            queryWrapper.eq(SpeechBaseDO::getStatus, publishStatus);
        }
        SpeechBaseDO speechBaseDO = speechBaseMapper.selectOne(queryWrapper);
        return SpeechBaseConvertor.toEntity(speechBaseDO);
    }

    @Override
    public SpeechBaseEntity getSpeechBaseById(Long id) {
        LambdaQueryWrapper<SpeechBaseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseDO::getId, id);
        SpeechBaseDO speechBaseDO = speechBaseMapper.selectOne(queryWrapper);
        return SpeechBaseConvertor.toEntity(speechBaseDO);
    }

    @Override
    public List<SpeechBaseEntity> getSpeechBaseByIds(Set<Long> ids) {
        LambdaQueryWrapper<SpeechBaseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(SpeechBaseDO::getId, ids);
        queryWrapper.eq(SpeechBaseDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        List<SpeechBaseDO> speechBaseList = speechBaseMapper.selectList(queryWrapper);
        return SpeechBaseConvertor.toEntityList(speechBaseList);
    }

    @Override
    public List<SpeechBaseEntity> getExportSpeech(SpeechManagerExportBO exportBO) {
        LambdaQueryWrapper<SpeechBaseDO> queryWrapper = new LambdaQueryWrapper<>();
        //话术id列表
        List<Long> ids = exportBO.getIds();
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        queryWrapper.in(SpeechBaseDO::getId, ids);
        queryWrapper.eq(SpeechBaseDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        List<SpeechBaseDO> speechBaseList = speechBaseMapper.selectList(queryWrapper);
        return SpeechBaseConvertor.toEntityList(speechBaseList);
    }

    @Override
    public List<SpeechBaseEntity> getAllPublishedSpeech() {
        LambdaQueryWrapper<SpeechBaseDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechBaseDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        queryWrapper.isNotNull(SpeechBaseDO::getPublishFilePath).ne(SpeechBaseDO::getPublishFilePath, "");
        List<SpeechBaseDO> speechBaseList = speechBaseMapper.selectList(queryWrapper);
        return SpeechBaseConvertor.toEntityList(speechBaseList);
    }

    @Override
    public void updateSpeechMode(SpeechBaseEntity speechBaseEntity) {
        LambdaUpdateWrapper<SpeechBaseDO> queryWrapper = new LambdaUpdateWrapper<>();
        queryWrapper.eq(SpeechBaseDO::getId, speechBaseEntity.getId());
        queryWrapper.set(SpeechBaseDO::getSpeechMode, speechBaseEntity.getSpeechMode());
        queryWrapper.set(SpeechBaseDO::getModelStage, speechBaseEntity.getModelStage());
        speechBaseMapper.update(null, queryWrapper);
    }

}
