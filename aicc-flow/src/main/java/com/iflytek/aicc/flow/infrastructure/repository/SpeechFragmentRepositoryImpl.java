package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.iflytek.aicc.flow.common.enums.SpeechManagerEnums;
import com.iflytek.aicc.flow.domain.manage.bo.SpeechFragmentListBO;
import com.iflytek.aicc.flow.domain.manage.entity.SpeechFragmentEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechFragmentRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechFragmentConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechFragmentDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechFragmentMapper;
import com.iflytek.fpva.uapcenter.pojo.UapUser;
import com.iflytek.fpva.uapcenter.service.UserOrgQueryApi;
import com.iflytek.medicalboot.core.exception.MedicalBusinessException;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Date 2023/8/14 18:43
 * @Description 话术片段
 */
@Service
public class SpeechFragmentRepositoryImpl implements SpeechFragmentRepository {
    private final SpeechFragmentMapper speechFragmentMapper;

    private final UserOrgQueryApi userOrgQueryApi;

    public SpeechFragmentRepositoryImpl(SpeechFragmentMapper speechFragmentMapper, UserOrgQueryApi userOrgQueryApi) {
        this.speechFragmentMapper = speechFragmentMapper;
        this.userOrgQueryApi = userOrgQueryApi;
    }

    @Override
    public PageList<SpeechFragmentEntity> query(SpeechFragmentListBO speechFragmentListBO, Page page) {
        PageList<SpeechFragmentDO> speechFragmentPageList = PageUtil.doPage(() -> speechFragmentMapper.selectByParam(speechFragmentListBO), page);
        List<SpeechFragmentDO> data = speechFragmentPageList.getData();
        Map<String, String> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(data)) {
            getUapUserMap(data, map);
        }
        return SpeechFragmentConvertor.toPageList(speechFragmentPageList, map);
    }

    @Override
    public void add(SpeechFragmentEntity speechFragmentEntity) {
        SpeechFragmentDO speechFragmentDO = SpeechFragmentConvertor.toDo(speechFragmentEntity);
        LambdaQueryWrapper<SpeechFragmentDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechFragmentDO::getName, speechFragmentEntity.getName());
        lambdaQueryWrapper.eq(SpeechFragmentDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        Integer count = speechFragmentMapper.selectCount(lambdaQueryWrapper);
        if (count > 0) {
            throw new MedicalBusinessException("名称重复");
        }

        speechFragmentMapper.insert(speechFragmentDO);
    }

    @Override
    public int delete(SpeechFragmentEntity speechFragmentEntity) {
        SpeechFragmentDO speechFragmentDO = SpeechFragmentConvertor.toDo(speechFragmentEntity);
        return speechFragmentMapper.updateById(speechFragmentDO);
    }

    @Override
    public SpeechFragmentEntity getById(Long id) {
        SpeechFragmentDO speechFragmentDO = speechFragmentMapper.selectById(id);
        return SpeechFragmentConvertor.toEntity(speechFragmentDO);
    }

    @Override
    public void updateById(SpeechFragmentEntity speechFragmentEntity) {
        SpeechFragmentDO speechFragmentDO = SpeechFragmentConvertor.toDo(speechFragmentEntity);
        LambdaQueryWrapper<SpeechFragmentDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(SpeechFragmentDO::getName, speechFragmentEntity.getName());
        lambdaQueryWrapper.eq(SpeechFragmentDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_NORMAL.getCode());
        lambdaQueryWrapper.ne(SpeechFragmentDO::getId, speechFragmentDO.getId());
        Integer count = speechFragmentMapper.selectCount(lambdaQueryWrapper);
        if (count > 0) {
            throw new MedicalBusinessException("名称重复");
        }
        speechFragmentMapper.updateById(speechFragmentDO);
    }

    @Override
    public List<SpeechFragmentEntity> listFragmentRel(SpeechFragmentListBO speechFragmentListBO) {
        List<SpeechFragmentDO> speechFragmentPageList = speechFragmentMapper.listFragmentRel(speechFragmentListBO);
        Map<String, String> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(speechFragmentPageList)) {
            getUapUserMap(speechFragmentPageList, map);
        }
        return SpeechFragmentConvertor.toList(speechFragmentPageList, map);
    }

    @Override
    public List<Long> listByFragmentName(SpeechFragmentListBO param) {

        List<SpeechFragmentDO> speechFragmentDOS = speechFragmentMapper.selectByParam(param);
        if (CollectionUtils.isNotEmpty(speechFragmentDOS)) {
            return speechFragmentDOS.stream().map(SpeechFragmentDO::getId).collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public Integer countFragmentRel(SpeechFragmentListBO speechFragmentListBO) {
        return speechFragmentMapper.countFragmentRel(speechFragmentMapper);
    }

    @Override
    public List<SpeechFragmentEntity> listById(List<Long> fragmentIds) {
        List<SpeechFragmentDO> speechFragmentDO = speechFragmentMapper.selectBatchIds(fragmentIds);
        return SpeechFragmentConvertor.toEntityList(speechFragmentDO);
    }

    @Override
    public List<SpeechFragmentEntity> list(SpeechFragmentListBO speechFragmentListBO, Long start) {
        List<SpeechFragmentDO> speechFragmentDO = speechFragmentMapper.list(speechFragmentListBO, start);
        Map<String, String> map = new HashMap<>(10);
        if (CollectionUtils.isNotEmpty(speechFragmentDO)) {
            getUapUserMap(speechFragmentDO, map);
        }
        return SpeechFragmentConvertor.toList(speechFragmentDO, map);
    }

    @Override
    public PageList<SpeechFragmentEntity> queryByCondition(SpeechFragmentListBO queryBO, Page page) {
        PageList<SpeechFragmentDO> speechFragmentPageList = PageUtil.doPage(() -> speechFragmentMapper.queryByCondition(queryBO), page);
        return SpeechFragmentConvertor.toPageList(speechFragmentPageList, new HashMap<>());
    }

    private void getUapUserMap(List<SpeechFragmentDO> data, Map<String, String> map) {
        Set<String> collectSet = new HashSet<>();
        for (SpeechFragmentDO datum : data) {
            collectSet.add(datum.getCreatedBy());
            collectSet.add(datum.getUpdatedBy());
            collectSet.add(datum.getPublishBy());
        }
        List<UapUser> uapUserList = userOrgQueryApi.getUsersByUserIds(Lists.newArrayList(collectSet), null);
        for (UapUser uapUser : uapUserList) {
            map.put(uapUser.getId(), uapUser.getName());
        }
    }
}
