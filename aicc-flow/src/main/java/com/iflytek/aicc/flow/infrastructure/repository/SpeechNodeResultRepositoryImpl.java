package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.iflytek.aicc.flow.domain.driven.entity.result.SpeechNodeResultEntity;
import com.iflytek.aicc.flow.domain.manage.repository.SpeechNodeResultRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.SpeechNodeResultConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.SpeechNodeResultDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechNodeResultMapper;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 描述：交互数据存储实现类
 * <AUTHOR>
 * @date 2023/9/11 09:42
 */
@Service
public class SpeechNodeResultRepositoryImpl implements SpeechNodeResultRepository {

    private final SpeechNodeResultMapper speechNodeResultMapper;

    public SpeechNodeResultRepositoryImpl(SpeechNodeResultMapper speechNodeResultMapper) {
       this.speechNodeResultMapper = speechNodeResultMapper;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(SpeechNodeResultEntity speechNodeResultEntity) {
        SpeechNodeResultDO speechNodeResultDO =  SpeechNodeResultConvertor.toDO(speechNodeResultEntity);
        if (null == speechNodeResultDO) {
            return;
        }
        speechNodeResultMapper.insert(speechNodeResultDO);
    }

    @Override
    public List<SpeechNodeResultEntity> selectSpeechNodeResultList(Long callId) {
        LambdaQueryWrapper<SpeechNodeResultDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SpeechNodeResultDO::getCallId, callId);
        queryWrapper.orderByAsc(SpeechNodeResultDO::getSeqNo);
        List<SpeechNodeResultDO> list = speechNodeResultMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        return list.stream().map(SpeechNodeResultConvertor::toEntity).collect(Collectors.toList());
    }
}
