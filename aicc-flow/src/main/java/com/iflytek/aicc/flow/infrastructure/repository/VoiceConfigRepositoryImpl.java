package com.iflytek.aicc.flow.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.iflytek.aicc.common.infrastructure.redis.RedisCacheService;
import com.iflytek.aicc.flow.common.enums.SpeechManagerEnums;
import com.iflytek.aicc.flow.domain.manage.bo.VoiceConfigListBO;
import com.iflytek.aicc.flow.domain.manage.entity.VoiceConfigEntity;
import com.iflytek.aicc.flow.domain.manage.repository.VoiceConfigRepository;
import com.iflytek.aicc.flow.infrastructure.repository.conveter.VoiceConfigConvertor;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.FilterSelectDO;
import com.iflytek.aicc.flow.infrastructure.repository.dataobject.VoiceConfigDO;
import com.iflytek.aicc.flow.infrastructure.repository.mapper.VoiceConfigMapper;
import com.iflytek.outbound.utils.JacksonUtils;
import com.iflytek.outbound.utils.PageUtil;
import com.iflytek.outbound.vo.Page;
import com.iflytek.outbound.vo.PageList;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.iflytek.aicc.common.constants.RedisCacheKey.SPEECH_VOICE_CONFIG_KEY;

/**
 * 发音人配置表
 *
 * <AUTHOR>
 * @date 2023/9/11 16:21
 */
@Service
public class VoiceConfigRepositoryImpl implements VoiceConfigRepository {
    private final VoiceConfigMapper voiceConfigMapper;
    private final RedisCacheService redisCacheService;
    /**
     * 缓存有效期 单位天
     */
    @Value("${redis.expire.time:30}")
    private Long redisExpireTime;

    private static final int TIYAN = 2;

    public VoiceConfigRepositoryImpl(VoiceConfigMapper voiceConfigMapper, RedisCacheService redisCacheService) {
        this.voiceConfigMapper = voiceConfigMapper;
        this.redisCacheService = redisCacheService;
    }

    @Override
    public void insert(VoiceConfigEntity voiceConfigEntity) {
        voiceConfigMapper.insert(VoiceConfigConvertor.toDO(voiceConfigEntity));
    }

    @Override
    public void update(VoiceConfigEntity voiceConfigEntity) {
        voiceConfigMapper.updateById(VoiceConfigConvertor.toDO(voiceConfigEntity));
    }

    @Override
    public int delete(Long id, String loginUserId) {
        LambdaUpdateWrapper<VoiceConfigDO> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(VoiceConfigDO::getId, id);
        wrapper.set(VoiceConfigDO::getDeleted, SpeechManagerEnums.SpeechStatusEnum.STATUS_DELETED.getCode());
        wrapper.set(VoiceConfigDO::getUpdatedBy, loginUserId);
        wrapper.set(VoiceConfigDO::getUpdatedTime, new Date());
        return voiceConfigMapper.update(null, wrapper);
    }

    @Override
    public PageList<VoiceConfigEntity> selectList(VoiceConfigListBO param, Page page) {
        LambdaQueryWrapper<VoiceConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VoiceConfigDO::getDeleted, 0);
        lambdaQueryWrapper.like(StringUtils.isNotBlank(param.getTtsVoiceName()), VoiceConfigDO::getTtsVoiceName, param.getTtsVoiceName());
        lambdaQueryWrapper.like(StringUtils.isNotBlank(param.getTtsVoiceCode()), VoiceConfigDO::getTtsVoiceCode, param.getTtsVoiceCode());
        lambdaQueryWrapper.eq(param.getEngineId() != null, VoiceConfigDO::getEngineId, param.getEngineId());
        lambdaQueryWrapper.eq(param.getVoiceType() != null, VoiceConfigDO::getVoiceType, param.getVoiceType());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(param.getPlatformCode()), VoiceConfigDO::getPlatformCode, param.getPlatformCode());
        lambdaQueryWrapper.eq(StringUtils.isNotBlank(param.getOrgId()), VoiceConfigDO::getOrgId, param.getOrgId());
        lambdaQueryWrapper.orderByDesc(VoiceConfigDO::getUpdatedTime).orderByDesc(VoiceConfigDO::getCreatedTime);

        PageList<VoiceConfigDO> pageList = PageUtil.doPage(() -> voiceConfigMapper.selectList(lambdaQueryWrapper), page);
        return VoiceConfigConvertor.toPageEntityList(pageList);
    }

    @Override
    public VoiceConfigEntity getById(Long id) {
        VoiceConfigDO voiceConfigDO = voiceConfigMapper.selectById(id);
        return VoiceConfigConvertor.toEntity(voiceConfigDO);
    }

    @Override
    public List<VoiceConfigEntity> getByEngineId(Long engineId) {
        LambdaQueryWrapper<VoiceConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VoiceConfigDO::getEngineId, engineId);
        lambdaQueryWrapper.eq(VoiceConfigDO::getDeleted, 0);
        List<VoiceConfigDO> voiceConfigList = voiceConfigMapper.selectList(lambdaQueryWrapper);
        return VoiceConfigConvertor.toEntityList(voiceConfigList);
    }

    @Override
    public List<VoiceConfigEntity> getCacheByEngineId(Long engineId) {
        List<VoiceConfigEntity> list =
                redisCacheService.getCacheHashValues(SPEECH_VOICE_CONFIG_KEY + engineId, VoiceConfigEntity.class);
        if (CollectionUtils.isNotEmpty(list)) {
            return list;
        }
        list = getByEngineId(engineId);
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, String> mapValues = list.stream().collect(Collectors.toMap(voiceEngineEntity
                            -> voiceEngineEntity.getId().toString(),
                    JacksonUtils::writeValueAsString));
            redisCacheService.setCacheHashValues(SPEECH_VOICE_CONFIG_KEY + engineId,
                    mapValues, redisExpireTime, TimeUnit.DAYS);
        }
        return list;
    }

    @Override
    public List<VoiceConfigEntity> getVoiceConfigList() {
        LambdaQueryWrapper<VoiceConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VoiceConfigDO::getDeleted, 0);
        List<VoiceConfigDO> list = voiceConfigMapper.selectList(lambdaQueryWrapper);
        return VoiceConfigConvertor.toEntityList(list);
    }

    @Override
    public VoiceConfigEntity getByCodeEngineId(String ttsVoiceCode, Long engineId) {
        LambdaQueryWrapper<VoiceConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VoiceConfigDO::getDeleted, 0);
        lambdaQueryWrapper.eq(VoiceConfigDO::getTtsVoiceCode, ttsVoiceCode);
        lambdaQueryWrapper.eq(VoiceConfigDO::getEngineId, engineId);
        VoiceConfigDO voiceConfigDO = voiceConfigMapper.selectOne(lambdaQueryWrapper);
        return VoiceConfigConvertor.toEntity(voiceConfigDO);
    }

    @Override
    public VoiceConfigEntity getByCode(String ttsVoiceCode) {
        LambdaQueryWrapper<VoiceConfigDO> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(VoiceConfigDO::getDeleted, 0);
        lambdaQueryWrapper.eq(VoiceConfigDO::getTtsVoiceCode, ttsVoiceCode).last("LIMIT 1");
        VoiceConfigDO voiceConfigDO = voiceConfigMapper.selectOne(lambdaQueryWrapper);
        return VoiceConfigConvertor.toEntity(voiceConfigDO);
    }

    @Override
    public Integer getVoiceCount(String platformCode, String orgId, Integer forkVoiceSource) {
        LambdaQueryWrapper<VoiceConfigDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VoiceConfigDO::getPlatformCode, platformCode);
        if (forkVoiceSource == TIYAN) {
            wrapper.eq(VoiceConfigDO::getCreatedBy, orgId);
        } else {
            wrapper.eq(VoiceConfigDO::getOrgId, orgId);
        }
        wrapper.eq(VoiceConfigDO::getForkVoiceSource, forkVoiceSource);
        wrapper.eq(VoiceConfigDO::getDeleted, 0);
        return voiceConfigMapper.selectCount(wrapper);
    }

    @Override
    public VoiceConfigEntity getForkVoice(String ttsVoiceCode, String platformCode) {
        LambdaQueryWrapper<VoiceConfigDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VoiceConfigDO::getPlatformCode, platformCode);
        wrapper.eq(VoiceConfigDO::getTtsVoiceCode, ttsVoiceCode);
        wrapper.eq(VoiceConfigDO::getDeleted, 0);
        VoiceConfigDO voiceConfigDO = voiceConfigMapper.selectOne(wrapper);
        return VoiceConfigConvertor.toEntity(voiceConfigDO);
    }

    @Override
    public List<FilterSelectDO> forkPlatformSelect() {
        return voiceConfigMapper.forkPlatformSelect();
    }

    @Override
    public List<FilterSelectDO> forkOrgSelect() {
        return voiceConfigMapper.forkOrgSelect();
    }
}
