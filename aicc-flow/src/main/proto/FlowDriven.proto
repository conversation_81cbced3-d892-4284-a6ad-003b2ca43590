syntax = "proto3";

option java_multiple_files = true;

//生成文件的包路径
//option java_package = "com.iflytek.aicc.flow";
//option java_outer_classname = "";
/**
  * 话术流程驱动请求入参
  * 具体参数说明见http接口文档
  */
message FlowDrivenRequest{
  /**
    * 请求协议字符串
   */
  string request = 1;
}

/**
  * 话术流程驱动请求出参
  */
message FlowDrivenResponse{
  /**
    * 0:成功 ，-1 或其他状态异常
   */
  int32 code = 1;
  /**
    * 状态描述
   */
  string message = 2;
  /**
    * 话术流程驱动返回数据对象
   */
  string data = 3;
}

service FlowDrivenService{
  // 查询对话驱动流程
  rpc flowDriven(FlowDrivenRequest) returns (stream FlowDrivenResponse);
}