<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.iflytek.aicc.flow.infrastructure.repository.mapper.SpeechNodeMapper">

    <insert id="batchSave">
        insert into tb_aicc_speech_node(tenant_id,revision,created_by,created_time,updated_by,updated_time,id,speech_id,node_name,node_type,parent_id,node_text)
        values
        <foreach collection="list" item="entity" separator=",">
            (#{entity.tenantId},#{entity.revision},#{entity.createdBy},#{entity.createdTime},#{entity.updatedBy},#{entity.updatedTime},#{entity.id},#{entity.speechId},#{entity.nodeName},#{entity.nodeType},#{entity.parentId},#{entity.nodeText})
        </foreach>
    </insert>

    <select id="getSpeechNodeInterface" resultType="com.iflytek.aicc.flow.api.vo.SpeechNodeInterfaceVO">
        select sn.node_name, sni.nlp_code as interfaceCode from tb_aicc_speech_node sn inner
        join tb_aicc_speech_nlp_interface sni on sn.updated_by  = sni.node_id
        where sn.speech_id = #{speechId} and sni.speech_id = #{speechId}
    </select>

</mapper>